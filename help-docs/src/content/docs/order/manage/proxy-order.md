---
title: 代客下单
description: 学习如何使用代客下单功能，为客户快速创建订单，包括客户选择、商品添加、配送设置等操作
---

# 代客下单

代客下单功能允许销售人员或客服人员代替客户在系统中创建订单。这个功能特别适用于电话订购、现场销售或客户无法自行操作系统的情况，提高了订单处理的灵活性和效率。

![代客下单主界面](../../../../assets/screenshots/order-proxy-create.png)

*图：代客下单主界面，展示完整的订单创建流程和各功能区域*

## 界面布局说明

代客下单界面采用分区域设计，便于操作人员按步骤完成订单创建：

### 顶部导航区域
- **标签页导航**：显示当前在"订单"模块下的"代客下单"功能
- **面包屑导航**：单门店 | 代客下单，清晰显示当前位置

### 主要功能区域

代客下单页面包含以下主要功能区域：

**基本信息区域**：
- **客户选择**：必填项，可搜索现有客户或点击"新建客户"按钮创建新客户
- **配送方式**：必填项，默认为"物流"，可选择其他配送方式
- **支付方式**：可选择"货到付款"等多种支付方式

**商品清单区域**：
- **全局仓库选择**：可选择仓库来更新所有商品价格
- **商品列表**：显示商品名称、规格、可用库存、数量、仓库、单价、小计等信息
- **商品管理**：支持删除和新增商品操作
- **金额统计**：显示商品总额、优惠金额、应收金额、实收金额等

**赠品清单区域**：
- 支持为订单添加赠品
- 赠品不计入订单金额

**备注说明区域**：
- 可填写订单相关的备注信息

**操作按钮区域**：
- **清除暂存**：清除已暂存的订单信息
- **暂存**：暂时保存订单信息，稍后继续编辑
- **提交保存**：正式提交订单

## 功能特点

1. **快速下单**：简化下单流程，快速为客户创建订单
2. **客户管理**：支持选择现有客户或快速创建新客户
3. **灵活配置**：支持多种配送方式和支付方式选择
4. **商品管理**：支持添加多个商品和赠品
5. **优惠设置**：支持设置订单优惠和折扣
6. **完整记录**：自动记录下单人员和操作时间

## 访问路径

1. 在左侧导航菜单中，点击**订单管理**
2. 在展开的子菜单中，选择**代客下单**

## 操作指南

### 步骤1：选择或创建客户

代客下单的第一步是确定订单的客户信息。

#### 选择现有客户

1. 在客户搜索框中输入客户姓名、手机号或客户编号
2. 从搜索结果中选择对应的客户
3. 系统会自动填充客户的基本信息和默认收货地址

![选择现有客户](/assets/images/order/manage/select-existing-customer.png)

#### 创建新客户

如果客户不存在于系统中，可以快速创建：

1. 点击**新建客户**按钮
2. 在弹出的表单中填写客户信息：

![新建客户界面](/assets/images/order/manage/create-new-customer.png)

**客户信息字段：**

| 字段名称 | 是否必填 | 说明 |
|----------|----------|------|
| 客户名称 | 是 | 客户的姓名或公司名称 |
| 登录账号 | 是 | 通常使用手机号作为登录账号 |
| 客户类型 | 是 | 选择客户所属类型 |
| 联系电话 | 是 | 客户的联系电话 |
| 收货地址 | 是 | 详细的收货地址信息 |
| 客户标签 | 否 | 为客户添加标签便于管理 |

3. 填写完成后点击**保存**创建客户

### 步骤2：设置配送和支付方式

![配送支付设置](/assets/images/order/manage/delivery-payment-settings.png)

**配送方式选择：**
- **快递配送**：选择快递公司进行配送
- **自提**：客户到店自行提取
- **同城配送**：本地配送服务
- **物流配送**：大件商品物流配送

**支付方式选择：**
- **在线支付**：微信支付、支付宝等
- **货到付款**：配送时现金支付
- **银行转账**：线下银行转账
- **账期支付**：企业客户账期结算

### 步骤3：添加商品

1. 在商品搜索框中输入商品名称或编码
2. 从搜索结果中选择需要的商品
3. 设置商品数量和单价
4. 如需添加多个商品，点击**添加商品**按钮

![添加商品界面](/assets/images/order/manage/add-products-to-order.png)

**商品信息设置：**

| 字段名称 | 说明 |
|----------|------|
| 商品名称 | 显示选中的商品名称 |
| 规格属性 | 选择商品的具体规格 |
| 数量 | 设置购买数量 |
| 单价 | 显示或调整商品单价 |
| 小计 | 自动计算该商品的总价 |

### 步骤4：设置优惠和赠品（可选）

#### 添加赠品

如果需要为客户提供赠品：

1. 在赠品区域点击**添加赠品**按钮
2. 选择赠品商品和数量
3. 赠品不计入订单金额

![添加赠品界面](/assets/images/order/manage/add-gifts-to-order.png)

#### 设置优惠

可以为订单设置各种优惠：

- **整单折扣**：设置整个订单的折扣比例
- **优惠金额**：直接减免指定金额
- **优惠券**：使用客户的优惠券
- **会员折扣**：应用会员专享折扣

### 步骤5：确认订单信息

在提交前，请仔细核对以下信息：

![订单确认界面](/assets/images/order/manage/confirm-proxy-order.png)

- **客户信息**：姓名、电话、收货地址
- **商品清单**：商品名称、数量、价格
- **配送方式**：配送方式和预计送达时间
- **支付方式**：支付方式和金额
- **优惠信息**：应用的优惠和折扣
- **订单总额**：最终需要支付的金额

### 步骤6：提交订单

1. 填写订单备注（可选）
2. 点击**提交订单**按钮
3. 系统生成订单并分配订单号
4. 自动发送订单确认通知给客户

## 权限说明

| 操作 | 所需权限 | 说明 |
|------|----------|------|
| 代客下单 | 订单管理-代客下单 | 可以为客户创建订单 |
| 创建客户 | 客户管理-新增 | 可以在下单时创建新客户 |
| 设置优惠 | 订单管理-优惠设置 | 可以为订单设置优惠 |
| 修改价格 | 订单管理-价格调整 | 可以调整商品价格 |

## 注意事项

1. **客户确认**：代客下单前应确认客户的真实购买意愿
2. **信息准确**：确保客户信息和收货地址准确无误
3. **库存检查**：下单前确认商品库存充足
4. **价格确认**：如有价格调整，应向客户说明原因
5. **记录保存**：系统会记录代客下单的操作人员和时间

## 常见问题

**Q: 代客下单后客户可以修改订单吗？**
A: 客户可以联系客服修改订单，但需要在订单未发货前进行。

**Q: 如何处理代客下单的退货？**
A: 退货流程与普通订单相同，客户可以申请退货或联系下单人员协助处理。

**Q: 代客下单是否支持分期付款？**
A: 根据系统配置和客户类型，部分客户可能支持分期付款或账期结算。

**Q: 下单时商品缺货怎么办？**
A: 系统会提示库存不足，可以调整数量或选择其他商品，也可以创建缺货订单等待补货。

## 相关功能

- [订货单管理](/order/manage/order-list)
- [客户管理](/customer/manage/customer-list)
- [商品管理](/goods/manage/publish-goods)
- [库存查询](/stock/section2/query)