---
title: 商品列表管理
description: 学习如何管理商品列表，包括商品的查看、编辑、删除和批量操作
tags: [goods-management, product-list, inventory]
keywords: [商品列表, 商品管理, 库存管理, 批量操作]
---

# 商品列表管理

商品列表是商品管理的核心功能，提供了完整的商品信息管理能力。通过商品列表，您可以查看所有商品的详细信息，进行商品的增删改查操作，以及执行各种批量管理任务。

![商品列表管理界面](../../../../assets/screenshots/goods-management-main.png)

*图：商品列表管理主界面，展示商品的基本信息、库存状态和操作功能*

## 界面概览

升辉ERP的商品管理界面采用现代化的设计，提供了直观的商品管理体验。主界面包含以下几个关键区域：

### 导航区域
- **顶部标签页**：商品、订单、员工、客户、采购、库存等主要功能模块
- **左侧菜单**：商品管理、价格管理等子功能菜单
- **面包屑导航**：显示当前页面位置，便于用户了解所在位置

### 功能操作区域
- **发布商品按钮**：位于页面右上角，用于快速创建新商品
- **搜索筛选区域**：提供关键词搜索、分类筛选、销售状态筛选等功能
- **批量操作工具**：支持批量上下架、批量编辑等操作

## 界面功能区域

### 顶部操作区域

**筛选条件**：
- **商品名称/编码搜索**：支持模糊搜索商品名称或商品编码
- **商品分类筛选**：按商品分类进行筛选
- **商品状态筛选**：筛选上架、下架或草稿状态的商品
- **库存状态筛选**：筛选有库存、无库存或库存不足的商品

**操作按钮**：
- **新增商品**：创建新的商品信息
- **批量操作**：对选中的商品执行批量操作
- **导入商品**：通过Excel文件批量导入商品
- **导出数据**：导出商品列表数据

### 商品列表区域

商品列表以表格形式展示，主要包含以下信息：

| 字段 | 说明 |
|------|------|
| 商品图片 | 商品的主图缩略图 |
| 商品名称 | 商品的完整名称 |
| 商品编码 | 系统生成或手动设置的商品编码 |
| 商品分类 | 商品所属的分类 |
| 销售价格 | 商品的销售价格 |
| 库存数量 | 当前可用库存数量 |
| 商品状态 | 上架、下架或草稿状态 |
| 创建时间 | 商品创建的时间 |
| 操作 | 编辑、删除、复制等操作按钮 |

## 功能特点

1. **多维度筛选**：支持按名称、分类、状态等多种条件筛选商品
2. **批量操作**：支持批量修改价格、状态、分类等信息
3. **快速搜索**：提供实时搜索功能，快速定位目标商品
4. **状态管理**：直观显示商品的上架状态和库存状态
5. **操作便捷**：提供快捷的编辑、复制、删除操作
6. **数据导入导出**：支持Excel格式的数据导入导出

## 访问路径

1. 在主导航中点击**商品**标签页
2. 在左侧菜单中选择**商品管理** > **商品列表**

## 操作指南

### 查看商品信息

1. **浏览商品列表**：在商品列表中可以看到所有商品的基本信息
2. **使用筛选条件**：通过顶部的筛选条件快速找到目标商品
3. **查看详细信息**：点击商品名称或编辑按钮查看完整的商品信息

### 新增商品

1. 点击页面右上角的**新增商品**按钮
2. 填写商品基本信息：
   - 商品名称（必填）
   - 商品编码（可自动生成）
   - 商品分类（必选）
   - 商品图片（建议上传）
3. 设置价格信息：
   - 销售价格（必填）
   - 成本价格
   - 市场价格
4. 配置库存信息：
   - 初始库存数量
   - 库存预警值
   - 库存单位
5. 填写商品描述和详情
6. 设置商品状态（上架/下架）
7. 点击**保存**完成商品创建

### 编辑商品

1. 在商品列表中找到需要编辑的商品
2. 点击操作列中的**编辑**按钮
3. 修改需要更新的商品信息
4. 点击**保存**确认修改

### 批量操作

1. **选择商品**：勾选需要批量操作的商品
2. **选择操作类型**：
   - 批量修改价格
   - 批量修改状态（上架/下架）
   - 批量修改分类
   - 批量删除
3. **执行操作**：确认操作参数后执行批量操作

### 商品状态管理

#### 上架商品
- 上架的商品会在前端商城中显示
- 客户可以浏览和购买上架的商品
- 上架商品会参与库存计算

#### 下架商品
- 下架的商品不会在前端显示
- 已下架商品的订单仍可正常处理
- 可以随时重新上架

#### 草稿状态
- 新创建但未完善的商品
- 草稿状态的商品不会显示在前端
- 需要完善信息后才能上架

### 库存管理

#### 库存显示
- **绿色**：库存充足
- **黄色**：库存不足（低于预警值）
- **红色**：无库存

#### 库存操作
- 点击库存数量可以快速调整库存
- 支持库存盘点和调整
- 自动记录库存变动历史

## 权限说明

| 操作 | 所需权限 | 说明 |
|------|----------|------|
| 查看商品列表 | 商品管理-查看 | 可以浏览商品列表和基本信息 |
| 新增商品 | 商品管理-新增 | 可以创建新的商品 |
| 编辑商品 | 商品管理-编辑 | 可以修改商品信息 |
| 删除商品 | 商品管理-删除 | 可以删除商品（软删除） |
| 批量操作 | 商品管理-批量操作 | 可以执行批量操作 |
| 导入导出 | 商品管理-导入导出 | 可以导入导出商品数据 |

## 注意事项

1. **商品编码唯一性**：商品编码在系统中必须唯一，不能重复
2. **分类设置**：建议为商品设置准确的分类，便于管理和搜索
3. **图片规范**：商品图片建议使用正方形，尺寸不小于800x800像素
4. **价格设置**：确保价格设置合理，避免出现负数或异常价格
5. **库存管理**：及时更新库存信息，避免超卖情况
6. **状态管理**：合理使用商品状态，确保前端显示正确

## 常见问题

**Q: 为什么新增的商品在前端看不到？**
A: 请检查商品状态是否为"上架"，只有上架的商品才会在前端显示。

**Q: 如何批量修改商品价格？**
A: 选择需要修改的商品，点击批量操作，选择"批量修改价格"，设置新价格或调整幅度。

**Q: 商品删除后可以恢复吗？**
A: 系统采用软删除机制，删除的商品可以在回收站中恢复。

**Q: 如何设置商品的库存预警？**
A: 在编辑商品时，设置"库存预警值"，当库存低于此值时系统会提醒。

## 相关功能

- [商品分类管理](/goods/category-management)
- [商品价格管理](/goods/price/price-management)
- [库存管理](/stock/index)
- [订单管理](/order/manage/order-list)
